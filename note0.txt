一、
说明：针对不同类型的PDF设计了两套处理流程（展示流程图）
针对扫描PDF的基于PP-StructureV3的文档分析流程（对于扫描件无法直接提取文本，且扫描件是中文居多，因此采用业界领先的文档分析工具PP-StructureV3）
针对原生PDF的基于PyMuPDF的直接提取流程（对于原生PDF，文本信息是内嵌的，为了文本提取的准确性和处理速度，采用高效的PyMuPDF库）
二、
展示两个脚本的处理结果
三、
分析当前的不足之处
对于扫描PDF，表格结构识别效果相对较差，结构化输出依赖于PPStructureV3模型的内部定义。改进：调整参数增强表格结构识别效果，自定义结构化输出格式以获得更加精炼的结果
对于原生PDF，提取出的文本是准确的，但返回的结果是一堆零散的文本块，丢失了文档原来的结构信息。改进：计划采用新的处理流程（展示流程图），LayoutParser版面识别+PyMuPDF文本提取，在保持原有的文本提取准确性的基础上保留文本结构信息