"""
原生PDF处理脚本
专门处理原生PDF文档，提取文本、表格、图片等结构化信息
"""

import os
import json
import logging
from pathlib import Path
from datetime import datetime
import fitz  # PyMuPDF
import pandas as pd
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

class NativePDFProcessor:
    """原生PDF处理器"""
    
    def __init__(self, config):
        self.config = config
        
    def extract_text_blocks(self, page):
        """提取文本块，过滤页眉页脚"""
        blocks = page.get_text("dict")["blocks"]

        # 调试模式：显示所有原始文本块
        debug_mode = self.config.get("debug", False)
        if debug_mode:
            logger.info(f"原始文本块数量: {len(blocks)}")
            for i, block in enumerate(blocks):
                if "lines" in block:
                    text_content = ""
                    for line in block["lines"]:
                        for span in line["spans"]:
                            text_content += span["text"]
                    logger.info(f"Block {i}: {text_content[:100]}... (bbox: {block['bbox']})")
        
        # 页面尺寸
        page_height = page.rect.height
        page_width = page.rect.width
        
        # 定义页眉页脚区域（更保守的设置）
        header_threshold = page_height * 0.05  # 上方5%
        footer_threshold = page_height * 0.95  # 下方5%
        
        filtered_blocks = []
        
        for block in blocks:
            if "lines" not in block:
                continue

            bbox = block["bbox"]
            x0, y0, x1, y1 = bbox

            # 根据配置决定是否过滤页眉页脚
            if self.config.get("filter_headers_footers", True) and (y0 < header_threshold or y1 > footer_threshold):
                # 检查是否是页码（通常在页脚中央或角落）
                text_content = ""
                for line in block["lines"]:
                    for span in line["spans"]:
                        text_content += span["text"]
                
                # 检查是否是页码（支持多种格式）
                page_patterns = [
                    r'^\s*\d+\s*$',                    # 纯数字: "5"
                    r'^\s*-\s*\d+\s*-\s*$',           # 带横线: "- 5 -"
                    r'^\s*Page\s+\d+\s*$',            # Page 5
                    r'^\s*\d+\s*/\s*\d+\s*$',         # 5/10
                    r'^\s*\d+\s+of\s+\d+\s*$',        # 5 of 10
                ]

                is_page_number = any(re.match(pattern, text_content.strip(), re.IGNORECASE)
                                   for pattern in page_patterns)
                if is_page_number:
                    continue

                # 检查是否是常见的页眉页脚内容
                header_footer_patterns = [
                    r'^\s*chapter\s+\d+\s*$',         # Chapter 1
                    r'^\s*section\s+\d+\s*$',         # Section 1
                    r'^\s*\d{4}-\d{2}-\d{2}\s*$',     # 日期格式
                    r'^\s*copyright\s+.*$',           # Copyright
                    r'^\s*confidential\s*$',          # Confidential
                    r'^\s*draft\s*$',                 # Draft
                ]

                is_header_footer = any(re.match(pattern, text_content.strip(), re.IGNORECASE)
                                     for pattern in header_footer_patterns)
                if is_header_footer:
                    continue

                # 如果在页眉页脚区域且文本很短，跳过
                if len(text_content.strip()) < 5:  # 降低阈值，避免过度过滤
                    continue
            
            # 过滤极端边缘内容（更宽松的边距检查）
            # 只过滤真正在页面边缘的内容
            extreme_margin = page_width * 0.02  # 只过滤最边缘2%的内容
            if x0 < extreme_margin or x1 > page_width - extreme_margin:
                continue
                
            filtered_blocks.append(block)
            
        return filtered_blocks
    
    def extract_tables(self, page):
        """提取表格"""
        tables = []
        try:
            # 使用PyMuPDF的表格提取功能
            page_tables = page.find_tables()
            
            for i, table in enumerate(page_tables):
                table_data = table.extract()
                if table_data:
                    # 转换为HTML格式
                    df = pd.DataFrame(table_data[1:], columns=table_data[0])
                    html_content = df.to_html(index=False, border=1)
                    
                    tables.append({
                        "table_id": f"table_{i}",
                        "html_content": html_content,
                        "bbox": table.bbox,
                        "row_count": len(table_data),
                        "col_count": len(table_data[0]) if table_data else 0
                    })
        except Exception as e:
            logger.warning(f"表格提取失败: {e}")
            
        return tables
    
    def extract_images(self, page, img_dir, page_num):
        """提取图片"""
        images = []
        try:
            image_list = page.get_images()
            
            for i, img in enumerate(image_list):
                xref = img[0]
                pix = fitz.Pixmap(page.parent, xref)
                
                if pix.n - pix.alpha < 4:  # 确保不是CMYK
                    img_filename = f"page_{page_num}_img_{i}.png"
                    img_path = img_dir / img_filename
                    pix.save(str(img_path))
                    
                    images.append({
                        "image_id": f"img_{i}",
                        "filename": img_filename,
                        "bbox": img[1:5] if len(img) > 4 else None,
                        "size": (pix.width, pix.height)
                    })
                
                pix = None
        except Exception as e:
            logger.warning(f"图片提取失败: {e}")
            
        return images
    
    def process_pdf(self, pdf_path, output_dir):
        """处理PDF文件"""
        pdf_name = Path(pdf_path).stem
        output_dir = Path(output_dir)
        
        # 创建输出目录
        output_dir.mkdir(exist_ok=True, parents=True)
        img_dir = output_dir / "imgs"
        img_dir.mkdir(exist_ok=True)
        
        jsonl_path = output_dir / f"{pdf_name}.jsonl"
        md_path = output_dir / f"{pdf_name}.md"
        
        try:
            with fitz.open(pdf_path) as doc, \
                 open(jsonl_path, 'w', encoding='utf-8') as jsonl_file, \
                 open(md_path, 'w', encoding='utf-8') as md_file:
                
                logger.info(f"处理原生PDF: {pdf_name} ({len(doc)} pages)")
                md_file.write(f"# {pdf_name}\n\n")
                
                for page_num in range(len(doc)):
                    page = doc.load_page(page_num)
                    
                    # 提取文本块（过滤页眉页脚）
                    text_blocks = self.extract_text_blocks(page)
                    
                    # 提取表格
                    tables = self.extract_tables(page)
                    
                    # 提取图片
                    images = self.extract_images(page, img_dir, page_num + 1)
                    
                    # 构建内容块
                    content_blocks = []
                    
                    # 处理文本块
                    for i, block in enumerate(text_blocks):
                        text_content = ""
                        for line in block["lines"]:
                            for span in line["spans"]:
                                text_content += span["text"]
                        
                        if text_content.strip():
                            content_blocks.append({
                                "block_id": f"{pdf_name}_p{page_num + 1}_b{i}",
                                "block_type": "text",
                                "content": text_content.strip(),
                                "bbox": block["bbox"],
                                "confidence": None
                            })
                    
                    # 创建页面条目
                    entry = {
                        "document_id": pdf_name,
                        "page_number": page_num + 1,
                        "timestamp": datetime.now().isoformat(),
                        "source_type": "pdf_native",
                        "processing_pipeline": "PyMuPDF",
                        "content_blocks": content_blocks,
                        "tables": tables,
                        "images": images,
                        "metadata": {
                            "total_blocks": len(content_blocks),
                            "total_tables": len(tables),
                            "total_images": len(images),
                            "page_size": {
                                "width": page.rect.width,
                                "height": page.rect.height
                            }
                        }
                    }
                    
                    # 写入JSONL
                    jsonl_file.write(json.dumps(entry, ensure_ascii=False) + '\n')
                    
                    # 写入Markdown
                    md_file.write(f"## Page {page_num + 1}\n\n")
                    
                    # 写入文本内容
                    for block in content_blocks:
                        md_file.write(f"{block['content']}\n\n")
                    
                    # 写入表格
                    for table in tables:
                        md_file.write(f"{table['html_content']}\n\n")
                    
                    # 写入图片引用
                    for image in images:
                        md_file.write(f"![Image](imgs/{image['filename']})\n\n")
                    
                    logger.info(f"处理页面 {page_num + 1}/{len(doc)}")
                    
        except Exception as e:
            logger.error(f"处理异常: {str(e)}", exc_info=True)

def main():
    """主函数"""
    
    config = {
        "input_path": "./input_native",
        "output_dir": "./output_native",
        "filter_headers_footers": False,  # 推荐：完全不过滤，保持信息完整性
        "extract_tables": True,
        "extract_images": True,
        "language": "english",
        "debug": False  # 生产环境关闭调试
    }
    
    processor = NativePDFProcessor(config)
    
    input_path = Path(config["input_path"])
    if input_path.is_file():
        processor.process_pdf(str(input_path), config["output_dir"])
    else:
        pdf_files = list(input_path.glob("*.pdf"))
        logger.info(f"发现 {len(pdf_files)} 个PDF文件")
        
        for pdf_file in pdf_files:
            processor.process_pdf(str(pdf_file), config["output_dir"])

if __name__ == '__main__':
    main()
