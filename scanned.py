import os
import json
from pathlib import Path
from paddleocr import PPStructureV3
from PIL import Image

def process_and_organize_outputs(input_dir="input", output_dir="output"):
    """
    批量处理PDF，并将不同类型的输出（JSON, Markdown, 提取的图片, 版面分析图）
    整理到结构清晰的目录中。
    """
    # --- 1. 初始化模型 ---
    print("正在初始化 PP-StructureV3 模型...")
    pipeline = PPStructureV3(
        use_doc_orientation_classify=True,  # 文档方向识别
        use_table_recognition=True,         # 表格识别
        use_doc_unwarping=False,            # 文档展平
        use_seal_recognition=False          # 印章识别
    )
    print("模型初始化完成。")

    # --- 2. 准备目录 ---
    if not os.path.exists(input_dir):
        print(f"错误：输入目录 '{input_dir}' 不存在。")
        return
    os.makedirs(output_dir, exist_ok=True)

    # --- 3. 遍历PDF ---
    pdf_files = [f for f in os.listdir(input_dir) if f.lower().endswith('.pdf')]
    if not pdf_files:
        print(f"在 '{input_dir}' 目录中没有找到PDF文件。")
        return
    
    print(f"找到 {len(pdf_files)} 个PDF文件待处理。")

    for filename in pdf_files:
        pdf_path = os.path.join(input_dir, filename)
        doc_name = Path(pdf_path).stem
        print(f"\n--- 开始处理文档: {filename} ---")

        doc_output_dir = os.path.join(output_dir, doc_name)
        os.makedirs(doc_output_dir, exist_ok=True)

        try:
            results_iterator = pipeline.predict_iter(input=pdf_path)
            
            all_pages_json_data = []
            all_pages_markdown_info = []
            page_count = 0
            
            print("开始逐页分析...")
            for page_result in results_iterator:
                page_count += 1
                print(f"  正在处理第 {page_count} 页...")
                
                # a) 聚合JSON和Markdown信息
                all_pages_json_data.append(page_result.json)
                markdown_info = page_result.markdown
                all_pages_markdown_info.append(markdown_info)
                
                # b) 保存从Markdown中提取的图片到 'imgs' 子目录
                if "markdown_images" in markdown_info:
                    for img_name, img_data in markdown_info["markdown_images"].items():
                        image_save_path = os.path.join(doc_output_dir, img_name)
                        image_dir = os.path.dirname(image_save_path)
                        os.makedirs(image_dir, exist_ok=True)
                        if isinstance(img_data, Image.Image):
                            img_data.save(image_save_path)
                
                # c) 保存版面分析可视化图片到 'layout_visualizations' 子目录
                vis_images = page_result.img
                if "layout_order_res" in vis_images:
                    vis_dir = os.path.join(doc_output_dir, "layout_visualizations")
                    os.makedirs(vis_dir, exist_ok=True)
                    vis_save_path = os.path.join(vis_dir, f"page_{page_count}.png")
                    vis_images["layout_order_res"].save(vis_save_path)
                    print(f"    - 已保存版面分析图: {vis_save_path}")

            print(f"文档共有 {page_count} 页。正在整合并保存最终文件...")
            json_save_path = os.path.join(doc_output_dir, f"{doc_name}.json")
            with open(json_save_path, 'w', encoding='utf-8') as f:
                json.dump(all_pages_json_data, f, ensure_ascii=False, indent=4)
            print(f"JSON 结果已保存到: {json_save_path}")

            full_markdown_content = pipeline.concatenate_markdown_pages(all_pages_markdown_info)
            md_save_path = os.path.join(doc_output_dir, f"{doc_name}.md")
            with open(md_save_path, 'w', encoding='utf-8') as f:
                f.write(full_markdown_content)
            print(f"Markdown 结果已保存到: {md_save_path}")
            print(f"--- 文档: {filename} 处理完成 ---")

        except Exception as e:
            print(f"处理文档 {filename} 时发生严重错误: {e}")
    print("\n所有任务已完成。")


if __name__ == "__main__":
    process_and_organize_outputs()